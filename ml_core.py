import numpy as np
import pandas as pd
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error

# Import SAITS models
try:
    from saits_model import SAITSRegressor
    SAITS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Full SAITS not available - {e}")
    SAITS_AVAILABLE = False

try:
    from simple_saits import SimpleSAITSRegressor
    SIMPLE_SAITS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Simple SAITS not available - {e}")
    SIMPLE_SAITS_AVAILABLE = False

# --- PLUG-AND-PLAY MODEL REGISTRY ---
MODEL_REGISTRY = {
    'xgboost': {
        'name': 'XGBoost',
        'model_class': XGBRegressor,
        'hyperparameters': {
            'n_estimators': {'type': int, 'default': 300, 'min': 50, 'max': 2000, 'prompt': "Number of estimators"},
            'learning_rate': {'type': float, 'default': 0.05, 'min': 0.01, 'max': 0.3, 'prompt': "Learning rate"},
            'max_depth': {'type': int, 'default': 6, 'min': 3, 'max': 15, 'prompt': "Max tree depth"},
            'subsample': {'type': float, 'default': 1.0, 'min': 0.5, 'max': 1.0, 'prompt': "Subsample ratio"},
            'colsample_bytree': {'type': float, 'default': 1.0, 'min': 0.5, 'max': 1.0, 'prompt': "Column sample per tree"},
            'reg_alpha': {'type': float, 'default': 0, 'min': 0, 'max': 10, 'prompt': "L1 regularization"},
            'reg_lambda': {'type': float, 'default': 1, 'min': 0, 'max': 10, 'prompt': "L2 regularization"},
        },
        'fixed_params': {'random_state': 42, 'early_stopping_rounds': 50},
        'gpu_check': {
            'param': 'tree_method',
            'gpu_value': 'gpu_hist',
            'cpu_value': 'hist'
        }
    },
    'lightgbm': {
        'name': 'LightGBM',
        'model_class': LGBMRegressor,
        'hyperparameters': {
            'n_estimators': {'type': int, 'default': 300, 'min': 50, 'max': 2000, 'prompt': "Number of estimators"},
            'learning_rate': {'type': float, 'default': 0.1, 'min': 0.01, 'max': 0.3, 'prompt': "Learning rate"},
            'max_depth': {'type': int, 'default': -1, 'min': -1, 'max': 15, 'prompt': "Max tree depth (-1 no limit)"},
            'min_child_samples': {'type': int, 'default': 20, 'min': 5, 'max': 100, 'prompt': "Min child samples"},
        },
        'fixed_params': {'random_state': 42},
        'gpu_check': {
            'param': 'device',
            'gpu_value': 'gpu',
            'cpu_value': 'cpu'
        }
    },
    'catboost': {
        'name': 'CatBoost',
        'model_class': CatBoostRegressor,
        'hyperparameters': {
            'iterations': {'type': int, 'default': 1000, 'min': 100, 'max': 5000, 'prompt': "Iterations"},
            'learning_rate': {'type': float, 'default': None, 'min': 0.01, 'max': 0.3, 'prompt': "Learning rate"},
            'depth': {'type': int, 'default': 6, 'min': 3, 'max': 12, 'prompt': "Tree depth"},
            'l2_leaf_reg': {'type': float, 'default': 3, 'min': 1, 'max': 10, 'prompt': "L2 regularization"},
        },
        'fixed_params': {'random_state': 42, 'verbose': 0, 'early_stopping_rounds': 50},
        'gpu_check': {
            'param': 'task_type',
            'gpu_value': 'GPU',
            'cpu_value': 'CPU'
        }
    }
}

# Add SAITS models to the registry if available
if SAITS_AVAILABLE:
    MODEL_REGISTRY['saits'] = {
        'name': 'SAITS',
        'model_class': SAITSRegressor,
        'hyperparameters': {
            'sequence_length': {'type': int, 'default': 50, 'min': 10, 'max': 200, 'prompt': "Sequence length for time series"},
            'n_groups': {'type': int, 'default': 1, 'min': 1, 'max': 5, 'prompt': "Number of attention groups"},
            'n_group_inner_layers': {'type': int, 'default': 1, 'min': 1, 'max': 3, 'prompt': "Layers per group"},
            'd_model': {'type': int, 'default': 64, 'min': 32, 'max': 256, 'prompt': "Model dimension"},
            'd_inner': {'type': int, 'default': 128, 'min': 64, 'max': 512, 'prompt': "Inner dimension"},
            'n_head': {'type': int, 'default': 4, 'min': 2, 'max': 16, 'prompt': "Number of attention heads"},
            'dropout': {'type': float, 'default': 0.1, 'min': 0.0, 'max': 0.5, 'prompt': "Dropout rate"},
            'epochs': {'type': int, 'default': 100, 'min': 10, 'max': 500, 'prompt': "Training epochs"},
            'batch_size': {'type': int, 'default': 32, 'min': 8, 'max': 128, 'prompt': "Batch size"},
            'learning_rate': {'type': float, 'default': 0.001, 'min': 0.0001, 'max': 0.01, 'prompt': "Learning rate"},
            'patience': {'type': int, 'default': 10, 'min': 5, 'max': 50, 'prompt': "Early stopping patience"},
        },
        'fixed_params': {'random_state': 42, 'input_with_mask': True, 'param_sharing_strategy': 'inner_group', 'MIT': False},
        'gpu_check': None  # SAITS handles GPU detection internally
    }

if SIMPLE_SAITS_AVAILABLE:
    MODEL_REGISTRY['simple_saits'] = {
        'name': 'Simple SAITS',
        'model_class': SimpleSAITSRegressor,
        'hyperparameters': {
            'sequence_length': {'type': int, 'default': 50, 'min': 10, 'max': 200, 'prompt': "Sequence length for time series"},
            'd_model': {'type': int, 'default': 64, 'min': 32, 'max': 256, 'prompt': "Model dimension"},
            'n_head': {'type': int, 'default': 4, 'min': 2, 'max': 16, 'prompt': "Number of attention heads"},
            'n_layers': {'type': int, 'default': 2, 'min': 1, 'max': 6, 'prompt': "Number of transformer layers"},
            'dropout': {'type': float, 'default': 0.1, 'min': 0.0, 'max': 0.5, 'prompt': "Dropout rate"},
            'epochs': {'type': int, 'default': 100, 'min': 10, 'max': 500, 'prompt': "Training epochs"},
            'batch_size': {'type': int, 'default': 32, 'min': 8, 'max': 128, 'prompt': "Batch size"},
            'learning_rate': {'type': float, 'default': 0.001, 'min': 0.0001, 'max': 0.01, 'prompt': "Learning rate"},
            'patience': {'type': int, 'default': 10, 'min': 5, 'max': 50, 'prompt': "Early stopping patience"},
        },
        'fixed_params': {'random_state': 42},
        'gpu_check': None  # Simple SAITS handles GPU detection internally
    }

def evaluate_model_comprehensive(model, X_train, y_train, X_val, y_val):
    """Return MAE, RMSE, R2 and a composite score."""
    y_pred = model.predict(X_val)
    mae = mean_absolute_error(y_val, y_pred)
    rmse = np.sqrt(mean_squared_error(y_val, y_pred))
    r2 = r2_score(y_val, y_pred)
    r2_penalty = (1 - r2) if r2 > 0 else 10
    composite = (mae * 0.5) + (rmse * 0.3) + (r2_penalty * 0.2)
    return {'mae': mae, 'rmse': rmse, 'r2': r2, 'composite_score': composite}

def impute_logs(df, feature_cols, target_col, models_to_run, well_cfg, prediction_mode):
    """Main imputation routine."""
    res = df.copy()
    feat_set = feature_cols + ['MD']
    print(f'--- Target Log: {target_col} ---')

    # Train data selection
    if well_cfg['mode'] == 'separated':
        train_df = res[res['WELL'].isin(well_cfg['training_wells'])]
    else:
        train_df = res
    train = train_df[train_df[target_col].notna()].copy()

    if train.empty:
        print("No training data.")
        return res, {}

    X = train[feat_set].apply(lambda c: c.fillna(c.mean()), axis=0)
    y = train[target_col]
    X_tr, X_val, y_tr, y_val = train_test_split(X, y, test_size=0.25, random_state=42)

    evals, trained = [], {}
    for name, model in models_to_run.items():
        try:
            # Handle different model types
            if hasattr(model, '__class__') and 'SAITS' in model.__class__.__name__:
                # SAITS models - use their own training interface
                model.fit(X_tr, y_tr, eval_set=[(X_val, y_val)], verbose=False)
            elif 'early_stopping_rounds' in model.get_params():
                # Traditional ML models with early stopping
                model.fit(X_tr, y_tr, eval_set=[(X_val, y_val)], verbose=False)
            else:
                # Traditional ML models without early stopping
                model.fit(X_tr, y_tr)

            ev = evaluate_model_comprehensive(model, X_tr, y_tr, X_val, y_val)
            ev['model_name'] = name
            evals.append(ev)
            trained[name] = model
            print(f'{name}: MAE={ev["mae"]:.3f}, R2={ev["r2"]:.3f}')
        except Exception as e:
            print(f'{name} failed: {e}')

    if not evals:
        print("All models failed.")
        return res, {}

    evals.sort(key=lambda x: x['composite_score'])
    best_name = evals[0]['model_name']
    best_model = trained[best_name]
    print(f'Best model: {best_name}')

    # Prediction
    if well_cfg['mode'] == 'separated':
        pred_mask = res['WELL'].isin(well_cfg['prediction_wells'])
    else:
        pred_mask = pd.Series(True, index=res.index)

    X_pred = res.loc[pred_mask, feat_set].apply(lambda c: c.fillna(c.mean()), axis=0)
    preds = best_model.predict(X_pred) if not X_pred.empty else np.array([])

    full_pred = pd.Series(np.nan, index=res.index)
    full_pred.loc[pred_mask] = preds

    imp_col = f'{target_col}_imputed'
    pred_col = f'{target_col}_pred'
    err_col = f'{target_col}_error'

    res[pred_col] = full_pred
    if prediction_mode == 3:
        res[imp_col] = full_pred
    else:
        res[imp_col] = res[target_col].fillna(full_pred)

    mask_orig = res[target_col].notna() & res[pred_col].notna()
    res[err_col] = np.nan
    res.loc[mask_orig, err_col] = np.abs(res.loc[mask_orig, target_col] - res.loc[mask_orig, pred_col])

    return res, {'target': target_col, 'evaluations': evals, 'best_model_name': best_name, 'trained_models': trained}
